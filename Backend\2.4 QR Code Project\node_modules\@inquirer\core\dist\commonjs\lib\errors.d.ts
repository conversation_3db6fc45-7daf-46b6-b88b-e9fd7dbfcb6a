export declare class AbortPromptError extends Error {
    name: string;
    message: string;
    constructor(options?: {
        cause?: unknown;
    });
}
export declare class CancelPromptError extends Error {
    name: string;
    message: string;
}
export declare class ExitPromptError extends Error {
    name: string;
}
export declare class HookError extends Error {
    name: string;
}
export declare class ValidationError extends Error {
    name: string;
}
