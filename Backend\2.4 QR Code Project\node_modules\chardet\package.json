{"name": "chardet", "version": "0.7.0", "homepage": "https://github.com/runk/node-chardet", "description": "Character detector", "keywords": ["encoding", "character", "utf8", "detector", "chardet", "icu"], "author": "<PERSON> <<EMAIL>>", "contributors": ["@spikying", "@wtgtybhertgeghgtwtg", "@suisho", "@seangarner", "@zevanty"], "devDependencies": {"github-publish-release": "^5.0.0", "mocha": "^5.2.0"}, "repository": {"type": "git", "url": "**************:runk/node-chardet.git"}, "bugs": {"mail": "<EMAIL>", "url": "http://github.com/runk/node-chardet/issues"}, "scripts": {"test": "mocha -R spec --recursive --bail", "release": "scripts/release"}, "main": "index.js", "engine": {"node": ">=4"}, "readmeFilename": "README.md", "directories": {"test": "test"}, "license": "MIT"}